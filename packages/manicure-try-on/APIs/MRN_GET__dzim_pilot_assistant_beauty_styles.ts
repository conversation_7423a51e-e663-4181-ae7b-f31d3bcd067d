// import axios from 'axios'
import { request } from "@mrn/mrn-utils";

export interface Query {
  /**
   * 类型，1-试发型，2-试美甲，3-试染发
   */
  type: number;
}
export interface StyleImagesItems {
  /**
   * 标签（e.g.参考价：¥128-¥198）
   */
  tags?: string[];
  /**
   * 标题（e.g.慵懒蛋卷烫）
   */
  title?: string;
  /**
   * 图片url
   */
  styleImageUrl?: string;
}
export interface ButtonInfo {
  /**
   * 提示（e.g. 免费领18元神券）
   */
  tips?: string;
  /**
   * 标题
   */
  title?: string;
  /**
   * 模板内容（e.g. 美发低到%s元起）
   */
  content?: string;
  /**
   * 跳链
   */
  jumpUrl?: string;
  /**
   * 模板内容中高亮部分（e.g. 37.9）
   */
  highlight?: string;
}
export interface Response {
  buttonInfo?: ButtonInfo;
  styleImages?: StyleImagesItems[];
}

// type: app
// apiId: 7310
// elink文档地址: https://elink.sankuai.com/main/api-doc-v3/list?groupId=621&id=7310
// 接口名称：样式数据查询

// export function MRN_GET__dzim_pilot_assistant_beauty_styles(
//   params: Query,
// ): Promise<Response> {
//   return request({
//     baseURL: 'https://mapi.dianping.com/',
//     url: '/dzim/pilot/assistant/beauty/styles',
//     method: 'GET',
//     params,
//     // options.removeToken 移除默认添加的token (iOS特有)
//     // options.removeUserId 移除默认添加的userId (iOS特有)
//     options: {
//       disableShark: false, // options.disableShark: bool 关闭走 Shark 通道 // iOS MRN 2.5.3 支持 默认网络请求走 Shark 通道，如特殊原因需要关闭时设置 disableShark: true
//       disableRisk: false, // / 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
//       registerCandyHost: false, // options.registerCandyHost: bool 注册当前URL的Host到CandyHost 即使用 MTGuard 签名
//     },
//   })
//     .then((result: any) => {
//       return result.data;
//     })
//     .catch((e: any) => {
//       e.userInfo = {
//         ...e.userInfo,
//         requestInfo: {
//           url: '/dzim/pilot/assistant/beauty/styles',
//         },
//       };
//       throw e;
//     });
// }

// mock 时替换原 function，获取elink上管理的mock数据
export function MRN_GET__dzim_pilot_assistant_beauty_styles(params: Query): Promise<Response> {
  return request({
    baseURL: 'https://elink.sankuai.com/',
    url: 'fbs/mock/group/621/dzim/pilot/assistant/beauty/styles',
    method: 'GET',
    params,
  }).then((response) => {
    return response.data;
  }) as any as Promise<Response>
}
