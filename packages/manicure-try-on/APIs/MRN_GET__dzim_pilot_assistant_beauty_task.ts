// import axios from 'axios'
import { request } from "@mrn/mrn-utils";

export interface Query {
  /**
   * 任务id
   */
  taskId: number;
  /**
   * 1-点评 2-美团
   */
  platform?: number;
}
export interface Response {
  /**
   * 结果url，任务状态为2时的处理结果
   */
  result?: string;
  /**
   * 任务状态，1:处理中，2:处理成功，3:处理失败
   */
  status?: number;
}

// type: app
// apiId: 7311
// elink文档地址: https://elink.sankuai.com/main/api-doc-v3/list?groupId=621&id=7311
// 接口名称：任务查询接口

// export function MRN_GET__dzim_pilot_assistant_beauty_task(
//   params: Query,
// ): Promise<Response> {
//   return request({
//     baseURL: 'https://mapi.dianping.com/',
//     url: '/dzim/pilot/assistant/beauty/task',
//     method: 'GET',
//     params,
//     // options.removeToken 移除默认添加的token (iOS特有)
//     // options.removeUserId 移除默认添加的userId (iOS特有)
//     options: {
//       disableShark: false, // options.disableShark: bool 关闭走 Shark 通道 // iOS MRN 2.5.3 支持 默认网络请求走 Shark 通道，如特殊原因需要关闭时设置 disableShark: true
//       disableRisk: false, // / 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
//       registerCandyHost: false, // options.registerCandyHost: bool 注册当前URL的Host到CandyHost 即使用 MTGuard 签名
//     },
//   })
//     .then((result: any) => {
//       return result.data;
//     })
//     .catch((e: any) => {
//       e.userInfo = {
//         ...e.userInfo,
//         requestInfo: {
//           url: '/dzim/pilot/assistant/beauty/task',
//         },
//       };
//       throw e;
//     });
// }

// mock 时替换原 function，获取elink上管理的mock数据
export function MRN_GET__dzim_pilot_assistant_beauty_task(params: Query): Promise<Response> {
  return request({
    baseURL: 'https://elink.sankuai.com/',
    url: '/fbs/mock/group/621/dzim/pilot/assistant/beauty/task',
    method: 'GET',
    params,
  }).then((response) => {
    return response.data;
  }) as any as Promise<Response>
}
