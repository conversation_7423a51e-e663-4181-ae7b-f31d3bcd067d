// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

let iconfont;
try {
  iconfont = require('@max/leez-icon/font.js').fonts;
} catch (e) {
  console.error('fail load @max/leez-icon iconfont');
}

module.exports = {
  name: 'manicure-try-on',
  fonts: {
    ...iconfont, // 新增的icon
  },
  main: 'src/app_mrn.tsx',
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    moduleName: 'manicure-try-on-detail',
    initialProperties: {
      hideNavigationBar: true,
      type: 1,
      originalUrl: 'https://p0.meituan.net/joyplaystatic/9476d357b583dac04df8fd4abdb5c1e22584403.png',
    },
  },
};
