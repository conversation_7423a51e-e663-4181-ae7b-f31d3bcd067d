.container {
  width: 622rpx;
  height: 903rpx;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden; /* 必须隐藏，以裁剪动画视图 */
}

.image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.animatedImageWrapper {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  /* 宽度由动画控制 */
  overflow: hidden;
}

/* 关键：新增的"保护套"样式 */
.staticImageHolder {
  position: relative;
  width: 622rpx; /* 与容器宽度保持一致，确保图片不被压缩 */
  height: 100%;
} 