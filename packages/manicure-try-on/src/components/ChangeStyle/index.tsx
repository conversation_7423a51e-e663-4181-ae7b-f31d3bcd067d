import { createElement, memo, useEffect, useRef, useState, forwardRef, useImperativeHandle } from '@max/max';
import { Animated } from '@max/mbc-animated';
import AnimateView from '@max/mbc-animate-view';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import styles from './index.module.scss';

interface ChangeStyleProps {
  /**
   * 图片URL列表, 如果不传入则使用const.ts中的默认图片
   */
  images?: string[];
  /**
   * 图片切换的间隔时间 (ms)
   * @default 3000
   */
  interval?: number;
  /**
   * 动画执行时长 (ms)
   * @default 500
   */
  animationDuration?: number;
}

export interface ChangeStyleHandles {
  stopAnimation: () => void;
}

const ChangeStyle = forwardRef<ChangeStyleHandles, ChangeStyleProps>((props, ref) => {
  const { images = [], interval = 3000, animationDuration = 500 } = props;
  const [currentIndex, setCurrentIndex] = useState(0);
  const animationValue = useRef(new Animated.Value(0)).current;

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const animationController = useRef<any>(null);

  useEffect(() => {
    if (images.length < 2) {
      return;
    }

    timerRef.current = setTimeout(() => {
      animationController.current = Animated.timing(animationValue, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: false,
      });

      animationController.current.start(() => {
        animationController.current = null;
        // 使用 requestAnimationFrame 来同步状态更新和动画重置
        // 这可以防止在组件重新渲染前，动画值过早重置而导致的闪烁
        requestAnimationFrame(() => {
          setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
          animationValue.setValue(0);
        });
      });
    }, interval);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (animationController.current) {
        animationController.current.stop();
      }
    };
  }, [currentIndex, images, interval, animationDuration, animationValue]);

  useImperativeHandle(ref, () => ({
    stopAnimation: () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (animationController.current) {
        animationController.current.stop();
        animationController.current = null;
      }
      // 重置动画到初始状态
      animationValue.setValue(0);
    },
  }));

  if (!images || images.length === 0) {
    return null;
  }

  if (images.length < 2) {
    return <Image className={styles.image} source={{ uri: images[0] }} resizeMode="cover" />;
  }

  const nextIndex = (currentIndex + 1) % images.length;

  const animatedStyle = {
    width: animationValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['100%', '0%'],
    }),
  };

  return (
    <View className={styles.container}>
      {/* 底层：下一张要显示的图片 */}
      <Image className={styles.image} key={nextIndex} source={{ uri: images[nextIndex] }} resizeMode="cover" />
      
      {/* 上层：动画遮罩，它的宽度会变化 */}
      <AnimateView viewId="animated-image-switcher" style={animatedStyle} className={styles.animatedImageWrapper}>
        {/* 新增：固定宽度的"保护套"，防止内部图片被压缩 */}
        <View className={styles.staticImageHolder}>
            <Image className={styles.image} key={currentIndex} source={{ uri: images[currentIndex] }} resizeMode="cover" />
        </View>
      </AnimateView>
    </View>
  );
});

export default memo(ChangeStyle);
