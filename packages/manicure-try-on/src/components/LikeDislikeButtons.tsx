import { createElement, memo, useCallback } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Toast from '@max/leez-toast';
import { MRN_GET__dzim_pilot_assistant_hair_task_like } from '@APIs/MRN_GET__dzim_pilot_assistant_hair_task_like';
import { REQUEST_PLATFORM } from '@/utils';

interface LikeDislikeButtonsProps {
  // 任务ID，用于点赞点踩接口
  taskId?: number;
}

function LikeDislikeButtons(props: LikeDislikeButtonsProps) {
  const { taskId = 0 } = props;

  // 处理点赞
  const handleLike = useCallback(async () => {
    try {
      if (taskId) {
        await MRN_GET__dzim_pilot_assistant_hair_task_like({
          taskId,
          platform: REQUEST_PLATFORM,
          operation: 1, // 1-点赞
        });
      }
    } catch (error) {
      console.error('点赞失败:', error);
    } finally {
      Toast.open({ title: '感谢你的反馈' });
    }
  }, [taskId]);

  // 处理点踩
  const handleDislike = useCallback(async () => {
    try {
      if (taskId) {
        await MRN_GET__dzim_pilot_assistant_hair_task_like({
          taskId,
          platform: REQUEST_PLATFORM,
          operation: 2, // 2-点踩
        });
      }
    } catch (error) {
      console.error('点踩失败:', error);
    } finally {
      Toast.open({ title: '感谢你的反馈' });
    }
  }, [taskId]);

  return (
    <View style={styles.container}>
      {/* 点赞按钮 */}
      <View style={styles.button} onClick={handleLike}>
        <Image
          style={styles.icon}
          source={{ uri: 'https://p0.meituan.net/ingee/fa926c22d3610d38ae830736c16ae918548.png' }} // 暂时使用空字符串占位
          resizeMode="contain"
        />
      </View>

      {/* 分割线 */}
      <View style={styles.line} />

      {/* 点踩按钮 */}
      <View style={styles.button} onClick={handleDislike}>
        <Image
          style={styles.icon}
          source={{ uri: 'https://p0.meituan.net/ingee/c8b5a93493e04cc1cd55ad63f247e099539.png' }} // 暂时使用空字符串占位
          resizeMode="contain"
        />
      </View>
    </View>
  );
}

const styles = remStyleSheet({
  container: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 7,
    zIndex: 10,
    borderRadius: 20,
    backgroundColor: '#00000066',
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#FFFFFF',
  },
  button: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 14,
    height: 14,
  },
  line: {
    width: 1,
    height: 16,
    marginHorizontal: 10,
    backgroundColor: '#FFFFFF',
    opacity: 0.5,
  },
});

export default memo(LikeDislikeButtons);
