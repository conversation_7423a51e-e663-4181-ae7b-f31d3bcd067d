import React, { useCallback, useMemo } from 'react'
import { StyleSheet, Text, View, TouchableOpacity } from '@mrn/react-native'
import NavigationBar from '@max/leez-navigation-bar'
import Sensor from '@max/mbc-sensor'
import { close, openUrl } from '@mrn/mrn-utils'
import { LinearGradient } from '@mrn/react-native-linear-gradient';

/**
 * 导航栏信息
 */

interface Props {
  onSelectPhoto?: ()=>void
  disabled?: boolean
}

const NavBar: React.FC<Props> = props => {
  const {onSelectPhoto, disabled} = props
  console.log(disabled,'__disabled')
  const actionTexts = useMemo(() => {
    return [{ text: '我的预约', textColor: '#111111' }]
  }, [])
  const onBackPress = useCallback(() => {
    close()
  }, [])

  const renderNav = useMemo(() => {
    return (
      <NavigationBar
        safeArea="normal"
        backIcon={{ name: 'fanhui' }}
        onBackPress={onBackPress}
      >
        <View style={styles.navRow}>
          <Text style={styles.titleText}>{'我的相册'}</Text>
          <LinearGradient
            colors={disabled ? ['#E5E5E5', '#E5E5E5'] : ['#FF8FB6', '#FF3C87']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradientBtn}
          >
            <TouchableOpacity
              style={styles.selectBtn}
              onPress={!disabled ? onSelectPhoto : undefined}
              disabled={disabled}
            >
              <Text style={[styles.selectBtnText, disabled && styles.selectBtnTextDisabled]}>选好啦</Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      </NavigationBar>
    )
  }, [actionTexts, onSelectPhoto, disabled, onBackPress ])

  return (
    <Sensor
      onAppear={() => {
        console.log('导航出现了')
      }}
      isInNativeView={true}
      once={true}
    >
      {renderNav}
    </Sensor>
  )
}


const styles = StyleSheet.create({
  title: {
    width: 148,
    height: 18
  },
  navWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff'
  },
  navRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: -12,
  },
  titleText: {
    alignSelf: 'center',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
  },
  gradientBtn: {
    borderRadius: 16,
  },
  selectBtn: {
    height: 31,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  selectBtnText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    opacity: 1,
  },
  selectBtnTextDisabled: {
    opacity: 0.5,
  },
})

export default React.memo(NavBar)
