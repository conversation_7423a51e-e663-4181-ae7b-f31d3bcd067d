import React, { useCallback } from 'react'
import { View, Text, Image, StyleSheet, TouchableOpacity } from '@mrn/react-native'
import { IExample } from '@/utils/util'
import ModalBaseContainer from '@max/leez-modal-base-container'
import TopView from '@max/leez-top-view';
import Icon from '@max/leez-icon';
import { LinearGradient } from '@mrn/react-native-linear-gradient';

interface Props {
  visible: boolean
  topPhotos: IExample[]
  bottomPhotos: IExample[]
  onClose: () => void
}

const styles = StyleSheet.create({
  modalWrap: {
    width: 331,
    borderRadius: 16,
    backgroundColor: '#fff',
    paddingTop: 15,
    paddingBottom: 18,
    paddingHorizontal: 18
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    color: '#111',
    marginBottom: 10
  },
  photoRow: {
    flexDirection: 'row',
    justifyContent: "space-between",
    marginBottom: 6
  },
  photo: {
    width: 86,
    height: 86,
    borderRadius: 8
  },
  btnRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    width: '100%'
  },
  btn: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6
  },
  btnPrimary: {
    color: '#fff'
  },
  btnText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#222'
  },
  btnTextPrimary: {
    color: '#fff'
  },
  saveBtn: {
    width:'100%',
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginTop: 7
  },
  saveBtnGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold',
    fontFamily:"PingFang SC"
  },
  exampleItemWrap: {
    alignItems: 'center',
  },
  exampleItemDescWrap: {
    marginTop: 3,
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center',
    alignItems: 'center'
  },
  exampleItemIcon: {
    width: 11,
    height: 11,
    resizeMode: 'contain',
  },
  exampleItemDescText: {
    fontSize: 12,
    color: '#555',
    fontFamily: 'PingFang SC',
  },
  modalHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  closeBtn: {
    marginTop: -15,
    marginRight: -18,
  },
  errorTitle: {
    marginTop: 3,
  },
})

const PhotoTipModal = (props: Props) => {
  const {visible,topPhotos,bottomPhotos , onClose } = props

  const ExampleItem = useCallback((item,i)=>{
    return (
      <View style={styles.exampleItemWrap}>
        <Image key={i} source={{ uri: item.url }} style={styles.photo} />
        <View style={styles.exampleItemDescWrap}>
          <Image source={{uri: item.icon}} style={styles.exampleItemIcon}/>
          <Text style={styles.exampleItemDescText}>{item.des}</Text>
        </View>
      </View>
    )
  },[])

  return (
    <TopView>
      <ModalBaseContainer
        visible={visible}
        useRNModal={false}
        type="fade"
        onMaskPress={onClose}
      >
        <View style={styles.modalWrap}>
          <View style={styles.modalHeaderRow}>
            <Text style={styles.title}>正确照片示例</Text>
            <TouchableOpacity
              onPress={() => {
                console.log('关闭弹框')
              }}
              style={styles.closeBtn}
            >
              <Icon name={'guanbixian'} type={'extraBody1'} size={32} />
            </TouchableOpacity>
          </View>
          <View style={styles.photoRow}>
            {topPhotos.map((item, i) => ExampleItem(item,i))}
          </View>
          <Text style={[styles.title, styles.errorTitle]}>错误照片示例</Text>
          <View style={styles.photoRow}>
            {bottomPhotos.map((item, i) => ExampleItem(item,i))}
          </View>
          <TouchableOpacity style={styles.saveBtn} onPress={() => {}}>
            <LinearGradient
              colors={['#FF8FB6', '#FF3C87']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.saveBtnGradient}
            >
              <Text style={styles.saveBtnText}>我知道了，开始拍摄</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ModalBaseContainer>
    </TopView>

  )
}

export default React.memo(PhotoTipModal)
