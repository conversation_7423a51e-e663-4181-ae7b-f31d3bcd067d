import React, { useState, useCallback } from 'react'
import { View, Text, TouchableOpacity, Image, StyleSheet } from '@mrn/react-native';
import ModalBaseContainer from '@max/leez-modal-base-container'

import { saveImageToPhotosAlbum } from '@max/meituan-uni-image';
import { downloadFile } from '@max/meituan-uni-network';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import Toast from '@max/leez-toast';

interface Props {
  visible: boolean;
  onClose: () => void;
  imageUrl: string;
}

const SaveModal = (props: Props) => {
  const { visible, imageUrl, onClose } = props
  // 保存图片到相册
  const handleSave = useCallback(async () => {
    try {
      const res = await downloadFile({ url:imageUrl });
      if (res && res.tempFilePath) {
        console.log(res.tempFilePath,'_res.tempFilePath')
        saveImageToPhotosAlbum({
          // _meituan: {
          sceneToken: 'dd-20f752dd436d1949',
          // },
          filePath: res.tempFilePath
        }).then((e)=>{
          Toast.open({
            title: '保存成功',
            content: "请在我的相册中查看",
          })
        }).catch((err)=>{
          console.log(err,'__err')
          Toast.open({
            title: '保存失败',
            content: err.errMsg,
          })
        });
      }
    } catch (e) {
      // 错误处理
    }

    // onClose()
  },[onClose])

  return (
    <ModalBaseContainer
      visible={visible}
      useRNModal={false}
      type="fade"
      onMaskPress={onClose}
    >
        <View style={styles.modalBox}>
          {/* 关闭按钮 */}
          <TouchableOpacity style={styles.closeBtn} onPress={onClose}>
            <Image source={{ uri: imageUrl }} style={styles.closeIcon} />
          </TouchableOpacity>
          {/* 发型图片 */}
          <Image source={imageUrl ? { uri: imageUrl } : { uri:'' }} style={styles.avatar} />
          {/* 保存按钮 */}
          <TouchableOpacity style={styles.saveBtn} onPress={handleSave}>
            <LinearGradient
              colors={['#FF8FB6', '#FF3C87']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.saveBtnGradient}
            >
              <Text style={styles.saveBtnText}>保存至相册</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
    </ModalBaseContainer>
  );
};

const styles = StyleSheet.create({
  modalBox: {
    width: 338,
    backgroundColor: '#fff',
    paddingHorizontal:21,
    paddingBottom: 21,
    borderRadius: 12,
    alignItems: 'center',
    position: 'relative',
  },
  closeBtn: {
    position: 'absolute',
    top: -31,
    right: 0,
    zIndex: 2
  },
  closeIcon: {
    width: 24,
    height: 24
  },
  avatar: {
    width: 302,
    height: 402.5 ,
    borderRadius: 12,
    marginTop: 18,
    marginBottom: 15,
    marginHorizontal: 18,
    resizeMode: 'cover'
  },
  saveBtn: {
    width: 299,
    height: 40,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    // marginBottom: 0
  },
  saveBtnGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold',
    fontFamily:"PingFang SC"
  },
  shareRow: {
    flexDirection: 'row',
    width: 299,
    marginTop: 18,
    // marginBottom: 20
  },
  channelItem: {
    alignItems: 'center',
    width: 64
  },
  channelIcon: {
    width: 50,
    height: 50,
    borderRadius:25,
    overflow:'hidden',
    marginBottom: 8
  },
  channelName: {
    fontSize: 12,
    color: '#666'
  }
});

export default React.memo(SaveModal)
