import { useCallback, useMemo } from '@max/max';
import { createElement, memo } from '@max/max';
import AnimateView from '@max/mbc-animate-view';

import { navigateBack } from '@max/meituan-uni-navigate';
import NavigationBar from '@max/leez-navigation-bar';
import View from '@hfe/max-view';

import Text from '@hfe/max-text'; 

import { IS_ANDROID, statusBarHeight } from '@/utils';

import styles from './index.module.scss';

interface NavBarProps {
  scrollY?: any;
  leftText?: string;
}

export const NavBarHeight = IS_ANDROID ? 44 : statusBarHeight + 44;


function NavBar(props: NavBarProps) {
  const { scrollY, leftText } = props;
  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);

  const animateStyle = {
    opacity: scrollY.interpolate({
      inputRange: [0, 50],
      outputRange: [0, 1],
    }),
  };
  console.log('scrollY', animateStyle);

    const renderNav = useMemo(() => {
      return (
        <NavigationBar safeArea="normal" backIcon={{ name: 'fanhui' }} onBackPress={onBackPress}>
          <Text>{leftText}</Text>
        </NavigationBar>
      );
    }, [onBackPress]);


  return (
    <View>
      {renderNav}
      <AnimateView className={styles.navWrapper} style={animateStyle} viewId="navBar">
        {renderNav}
      </AnimateView>
    </View>
  );
}

export default memo(NavBar);
