# Detail页面款式切换优化

## 概述

本次重构优化了Detail页面中的款式切换逻辑，实现了智能的状态管理和轮询机制，提升了用户体验。

## 主要改进

### 1. 款式状态管理系统

为每个款式维护独立的生成状态：

```typescript
enum StyleGenerationStatus {
  NOT_GENERATED = 'not_generated',    // 未生成
  GENERATING = 'generating',          // 生成中
  GENERATED = 'generated',            // 已生成
  FAILED = 'failed'                   // 生成失败
}

interface StyleState {
  status: StyleGenerationStatus;      // 当前状态
  generatedImageUrl?: string;         // 生成的图片URL
  taskId?: number;                    // 任务ID
  error?: string;                     // 错误信息
  lastGeneratedTime?: number;         // 最后生成时间戳
}
```

### 2. 智能切换逻辑

根据款式的当前状态采用不同的处理策略：

- **首次切换**：触发生成效果图接口 + 开始轮询生成结果
- **已生成**：直接显示效果图，无需重复生成
- **生成中**：继续现有的轮询逻辑
- **生成失败**：重新触发生成流程

### 3. 轮询管理优化

- 确保同时只有一个轮询任务在运行
- 切换款式时自动清理前一个款式的轮询
- 支持轮询任务的取消和超时处理
- 为每个轮询设置合理的重试机制

### 4. 用户体验提升

- **加载状态**：生成中的款式显示加载动画
- **成功状态**：已生成的款式立即显示效果图和闪亮效果
- **失败状态**：生成失败时显示错误提示和重试引导
- **原图切换**：切换到原图时立即响应

## 使用方式

### 基本用法

```tsx
<Detail 
  type={1} // 1-试发型，2-试美甲，3-试染发
  originalUrl="https://example.com/original.jpg"
/>
```

### 状态查询

组件内部提供了便捷的状态查询方法：

```typescript
// 获取当前显示的图片URL
const getCurrentImageUrl = () => {
  if (currentIndex === 0) return originalUrl;
  const styleState = styleStates.get(currentIndex);
  return styleState?.generatedImageUrl || originalUrl;
};

// 获取当前款式的状态
const getCurrentStyleState = () => {
  if (currentIndex === 0) return { status: StyleGenerationStatus.GENERATED };
  return styleStates.get(currentIndex) || { status: StyleGenerationStatus.NOT_GENERATED };
};
```

## 组件接口

### Detail组件Props

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | number | 是 | 试戴类型：1-试发型，2-试美甲，3-试染发 |
| originalUrl | string | 是 | 原始图片URL |

### Preview组件Props

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| imageUrl | string | 否 | 当前显示的图片URL |
| isLoading | boolean | 否 | 是否显示加载状态 |
| isComplete | boolean | 否 | 是否完成生成 |
| isFailed | boolean | 否 | 是否生成失败 |
| errorMessage | string | 否 | 错误信息 |
| styleInfo | StyleImagesItems | 否 | 款式信息数据 |
| taskId | number | 否 | 任务ID，用于点赞点踩 |

## 错误处理

系统提供了完善的错误处理机制：

1. **网络错误**：自动重试，超时后显示错误提示
2. **生成失败**：显示具体错误信息，支持重新生成
3. **轮询超时**：设置合理的超时时间，避免无限等待
4. **任务取消**：切换款式时自动取消前一个任务

## 性能优化

- **状态缓存**：已生成的效果图会被缓存，避免重复生成
- **轮询优化**：智能轮询间隔，减少不必要的API调用
- **内存管理**：及时清理无用的轮询任务和状态数据
- **组件优化**：使用memo和useCallback优化渲染性能

## 测试

提供了完整的单元测试覆盖：

```bash
# 运行测试
npm test Detail.test.tsx

# 测试覆盖率
npm run test:coverage
```

测试用例包括：
- 页面初始化
- 款式切换逻辑
- 生成成功/失败处理
- 轮询超时处理
- 错误状态显示
