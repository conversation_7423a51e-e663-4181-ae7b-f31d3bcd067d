import { createElement } from '@max/max';
import Detail, { StyleGenerationStatus } from '../index';

// Mock testing library functions for demonstration
const render = (component: any) => ({ getByText: (text: string) => ({ press: () => {} }) });
const fireEvent = { press: (element: any) => {} };
const waitFor = (callback: () => void) => Promise.resolve();

// Mock API calls
jest.mock('@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles', () => ({
  MRN_GET__dzim_pilot_assistant_beauty_styles: jest.fn(() => Promise.resolve({
    styleImages: [
      { title: '测试款式1', styleImageUrl: 'http://test1.jpg', tags: ['标签1'] },
      { title: '测试款式2', styleImageUrl: 'http://test2.jpg', tags: ['标签2'] }
    ]
  }))
}));

jest.mock('@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit', () => ({
  MRN_GET__dzim_pilot_assistant_hair_task_submit: jest.fn(() => Promise.resolve({
    taskId: 12345
  }))
}));

jest.mock('@APIs/MRN_GET__dzim_pilot_assistant_beauty_task', () => ({
  MRN_GET__dzim_pilot_assistant_beauty_task: jest.fn(() => Promise.resolve({
    status: 2,
    result: 'http://generated-image.jpg'
  }))
}));

// Mock navigation
jest.mock('@max/meituan-uni-navigate', () => ({
  navigateBack: jest.fn()
}));

// Mock system info
jest.mock('@max/meituan-uni-system', () => ({
  getSystemInfoSync: () => ({ screenWidth: 375 })
}));

describe('Detail页面款式切换逻辑测试', () => {
  const defaultProps = {
    type: 1,
    originalUrl: 'http://original.jpg'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该正确初始化页面状态', async () => {
    const { getByText } = render(<Detail {...defaultProps} />);

    await waitFor(() => {
      expect(getByText('AI试发型')).toBeTruthy();
    });
  });

  test('应该正确处理款式切换到原图', async () => {
    const { getByText } = render(<Detail {...defaultProps} />);

    await waitFor(() => {
      const originalButton = getByText('原图');
      fireEvent.press(originalButton);
      // 切换到原图应该立即完成，不触发生成
    });
  });

  test('应该正确处理首次切换到新款式', async () => {
    const { MRN_GET__dzim_pilot_assistant_hair_task_submit } = require('@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit');
    const { MRN_GET__dzim_pilot_assistant_beauty_task } = require('@APIs/MRN_GET__dzim_pilot_assistant_beauty_task');

    const { getByText } = render(<Detail {...defaultProps} />);

    await waitFor(() => {
      const styleButton = getByText('测试款式1');
      fireEvent.press(styleButton);
    });

    // 应该调用提交任务API
    expect(MRN_GET__dzim_pilot_assistant_hair_task_submit).toHaveBeenCalledWith({
      type: '1',
      platform: expect.any(Number),
      styleUrl: 'http://test1.jpg',
      originUrl: 'http://original.jpg'
    });

    // 应该调用轮询API
    await waitFor(() => {
      expect(MRN_GET__dzim_pilot_assistant_beauty_task).toHaveBeenCalledWith({
        taskId: 12345,
        platform: expect.any(Number)
      });
    });
  });

  test('应该正确处理生成失败的情况', async () => {
    const { MRN_GET__dzim_pilot_assistant_hair_task_submit } = require('@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit');

    // Mock失败的API调用
    MRN_GET__dzim_pilot_assistant_hair_task_submit.mockRejectedValueOnce(new Error('网络错误'));

    const { getByText } = render(<Detail {...defaultProps} />);

    await waitFor(() => {
      const styleButton = getByText('测试款式1');
      fireEvent.press(styleButton);
    });

    // 应该显示错误提示
    await waitFor(() => {
      expect(getByText('AI生成失败，请重试')).toBeTruthy();
    });
  });

  test('应该正确处理轮询超时', async () => {
    const { MRN_GET__dzim_pilot_assistant_beauty_task } = require('@APIs/MRN_GET__dzim_pilot_assistant_beauty_task');

    // Mock轮询一直返回处理中状态
    MRN_GET__dzim_pilot_assistant_beauty_task.mockResolvedValue({
      status: 1 // 处理中
    });

    const { getByText } = render(<Detail {...defaultProps} />);

    await waitFor(() => {
      const styleButton = getByText('测试款式1');
      fireEvent.press(styleButton);
    });

    // 等待轮询超时
    await waitFor(() => {
      expect(getByText('AI生成超时')).toBeTruthy();
    }, { timeout: 35000 }); // 轮询最多30秒
  });
});
