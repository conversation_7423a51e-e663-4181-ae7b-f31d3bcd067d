import { createElement, memo, useState, useEffect, useCallback } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import LText from '@max/leez-text';
import Toast from '@max/leez-toast';
import BlurView from '@max/leez-blur-view';
import LoadingView from '@max/leez-loading-view';
import { getSystemInfoSync } from '@max/meituan-uni-system';
import { StyleImagesItems } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import LikeDislikeButtons from '@/components/LikeDislikeButtons';
import StyleInfoCard from '@/components/StyleInfoCard';
import SaveImageModal from '@/components/SaveImageModal';
import { useStyleGeneration, StyleGenerationStatus } from '../../hooks/useStyleGeneration';

const { screenWidth } = getSystemInfoSync();
const IMAGE_WIDTH = screenWidth;
const IMAGE_HEIGHT = IMAGE_WIDTH * 1040 / 750;
const IMAGE_STYLE = { width: IMAGE_WIDTH, height: IMAGE_HEIGHT };

interface PreviewProps {
  // 原始图片URL
  originalImageUrl: string;
  // 当前选中的款式信息（null表示原图）
  currentStyle?: StyleImagesItems | null;
  // 试戴类型，1-试发型，2-试美甲，3-试染发
  type: number;
  // 是否是原图
  isOriginal?: boolean;
}

function Preview(props: PreviewProps) {
  const {
    originalImageUrl,
    currentStyle,
    type,
    isOriginal = false,
  } = props;

  const [showSaveModal, setShowSaveModal] = useState(false);

  // 使用自定义Hook管理款式生成
  const {
    status,
    generatedImageUrl,
    taskId,
    error,
    startGeneration,
    continuePolling,
    reset,
  } = useStyleGeneration();

  // 获取当前显示的图片URL
  const getCurrentImageUrl = useCallback(() => {
    if (isOriginal) {
      return originalImageUrl; // 原图
    }

    if (status === StyleGenerationStatus.GENERATED && generatedImageUrl) {
      return generatedImageUrl;
    }

    return originalImageUrl; // 默认显示原图
  }, [isOriginal, status, generatedImageUrl, originalImageUrl]);

  // 获取当前状态信息
  const isLoading = status === StyleGenerationStatus.GENERATING;
  const isComplete = status === StyleGenerationStatus.GENERATED;
  const isFailed = status === StyleGenerationStatus.FAILED;

  // 处理款式切换
  const handleStyleChange = useCallback(async () => {
    if (isOriginal || !currentStyle) {
      // 切换到原图，重置状态
      reset();
      return;
    }

    const styleImageUrl = currentStyle.styleImageUrl || '';

    // 根据当前状态决定处理策略
    if (status === StyleGenerationStatus.NOT_GENERATED) {
      // 首次切换到该款式：触发生成
      try {
        await startGeneration(originalImageUrl, styleImageUrl, type);
        console.log(`首次生成款式: ${currentStyle.title}`, currentStyle);
      } catch (error) {
        console.error('Failed to generate style image:', error);
        Toast.open({ title: 'AI生成失败，请重试' });
      }
    } else if (status === StyleGenerationStatus.GENERATED) {
      // 已生成过：直接显示，无需额外操作
      console.log(`切换到已生成款式: ${currentStyle.title}`, currentStyle);
    } else if (status === StyleGenerationStatus.GENERATING) {
      // 生成中：继续轮询（如果有taskId）
      if (taskId) {
        try {
          await continuePolling(taskId);
          console.log(`继续轮询款式: ${currentStyle.title}`, currentStyle);
        } catch (error) {
          console.error('Failed to poll task result:', error);
          Toast.open({ title: 'AI生成失败，请重试' });
        }
      }
    } else if (status === StyleGenerationStatus.FAILED) {
      // 生成失败：重新生成
      try {
        await startGeneration(originalImageUrl, styleImageUrl, type);
        console.log(`重新生成款式: ${currentStyle.title}`, currentStyle);
      } catch (error) {
        console.error('Failed to regenerate style image:', error);
        Toast.open({ title: 'AI生成失败，请重试' });
      }
    }
  }, [isOriginal, currentStyle, status, taskId, originalImageUrl, type, startGeneration, continuePolling, reset]);

  // 监听款式变化
  useEffect(() => {
    handleStyleChange();
  }, [handleStyleChange]);

  return (
    <View style={[styles.container, IMAGE_STYLE]}>
      {/* 主图片 */}
      <Image style={IMAGE_STYLE} source={{ uri: getCurrentImageUrl() }} resizeMode="cover" />

      {/* 完成态展示 */}
      {isComplete && (
        <>
          {/* 点赞点踩按钮 */}
          <LikeDislikeButtons taskId={taskId} />

          {/* 闪亮效果 */}
          <Image
            style={styles.sparkleEffect}
            source={{ uri: '' }} // 暂时使用空字符串占位
            resizeMode="contain"
          />
        </>
      )}

      {/* 加载状态覆盖层 */}
      {isLoading && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={5}
          />
          <View style={styles.loadingView}>
            <View style={styles.loadingWrap}>
              <LoadingView level="large" content="正在模拟效果" bgColorType="dark" />
            </View>
          </View>
        </>
      )}

      {/* 失败状态覆盖层 */}
      {isFailed && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={3}
          />
          <View style={styles.loadingView}>
            <View style={styles.errorWrap}>
              <Image
                style={styles.errorIcon}
                source={{ uri: 'https://p0.meituan.net/joyplaystatic/03a3ee04192d638227d5b6bc2176844e2106.png' }}
              />
              <LText style={styles.errorText}>
                {error || 'AI生成失败'}
              </LText>
              <LText style={styles.retryHint}>
                请重新选择款式重试
              </LText>
            </View>
          </View>
        </>
      )}

      {/* 款式信息介绍模块 - 放在主图片下方 */}
      {isComplete && currentStyle && (
        <StyleInfoCard
          style={styles.styleInfoCard}
          title={currentStyle.title}
          tags={currentStyle.tags}
        />
      )}

      {/* 保存图片弹窗 */}
      <SaveImageModal
        visible={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        imageUrl={getCurrentImageUrl()}
      />
    </View>
  );
}

const styles = remStyleSheet({
  container: {
    position: 'relative',
  },
  sparkleEffect: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 100,
    height: 100,
    marginTop: -50,
    marginLeft: -50,
    zIndex: 5,
  },
  loadingOverlay: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  loadingView: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  styleInfoCard: {
    position: 'absolute',
    bottom: 22,
    left: 0,
    right: 0,
  },
  errorWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  errorIcon: {
    width: 32,
    height: 32,
    marginBottom: 8,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  retryHint: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
  },
});

export default memo(Preview);