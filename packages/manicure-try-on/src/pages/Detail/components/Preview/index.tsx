import { createElement, memo, useState } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import LText from '@max/leez-text';
import BlurView from '@max/leez-blur-view';
import LoadingView from '@max/leez-loading-view';
import { getSystemInfoSync } from '@max/meituan-uni-system';
import { StyleImagesItems } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import LikeDislikeButtons from '@/components/LikeDislikeButtons';
import StyleInfoCard from '@/components/StyleInfoCard';
import SaveImageModal from '@/components/SaveImageModal';

const { screenWidth } = getSystemInfoSync();
const IMAGE_WIDTH = screenWidth;
const IMAGE_HEIGHT = IMAGE_WIDTH * 1040 / 750;
const IMAGE_STYLE = { width: IMAGE_WIDTH, height: IMAGE_HEIGHT };

interface PreviewProps {
  // 当前显示的图片URL
  imageUrl?: string;
  // 是否显示加载状态
  isLoading?: boolean;
  // 是否完成
  isComplete?: boolean;
  // 款式信息数据
  styleInfo?: StyleImagesItems;
  // 任务ID，用于点赞点踩
  taskId?: number;
  // 是否生成失败
  isFailed?: boolean;
  // 错误信息
  errorMessage?: string;
}

function Preview(props: PreviewProps) {
  const {
    imageUrl = '',
    isLoading = false,
    isComplete = false,
    styleInfo,
    taskId,
    isFailed = false,
    errorMessage,
  } = props;

  const [showSaveModal, setShowSaveModal] = useState(false);

  return (
    <View style={[styles.container, IMAGE_STYLE]}>
      {/* 主图片 */}
      <Image style={IMAGE_STYLE} source={{ uri: imageUrl }} resizeMode="cover" />

      {/* 完成态展示 */}
      {isComplete && (
        <>
          {/* 点赞点踩按钮 */}
          <LikeDislikeButtons taskId={taskId} />

          {/* 闪亮效果 */}
          <Image
            style={styles.sparkleEffect}
            source={{ uri: '' }} // 暂时使用空字符串占位
            resizeMode="contain"
          />
        </>
      )}

      {/* 加载状态覆盖层 */}
      {isLoading && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={5}
          />
          <View style={styles.loadingView}>
            <View style={styles.loadingWrap}>
              <LoadingView level="large" content="正在模拟效果" bgColorType="dark" />
            </View>
          </View>
        </>
      )}

      {/* 失败状态覆盖层 */}
      {isFailed && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={3}
          />
          <View style={styles.loadingView}>
            <View style={styles.errorWrap}>
              <Image
                style={styles.errorIcon}
                source={{ uri: 'https://p0.meituan.net/joyplaystatic/03a3ee04192d638227d5b6bc2176844e2106.png' }}
              />
              <LText style={styles.errorText}>
                {errorMessage || 'AI生成失败'}
              </LText>
              <LText style={styles.retryHint}>
                请重新选择款式重试
              </LText>
            </View>
          </View>
        </>
      )}

      {/* 款式信息介绍模块 - 放在主图片下方 */}
      {isComplete && styleInfo && (
        <StyleInfoCard
          style={styles.styleInfoCard}
          title={styleInfo.title}
          tags={styleInfo.tags}
        />
      )}

      {/* 保存图片弹窗 */}
      <SaveImageModal
        visible={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        imageUrl={imageUrl}
      />
    </View>
  );
}

const styles = remStyleSheet({
  container: {
    position: 'relative',
  },
  sparkleEffect: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 100,
    height: 100,
    marginTop: -50,
    marginLeft: -50,
    zIndex: 5,
  },
  loadingOverlay: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  loadingView: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  styleInfoCard: {
    position: 'absolute',
    bottom: 22,
    left: 0,
    right: 0,
  },
  errorWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  errorIcon: {
    width: 32,
    height: 32,
    marginBottom: 8,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  retryHint: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
  },
});

export default memo(Preview);