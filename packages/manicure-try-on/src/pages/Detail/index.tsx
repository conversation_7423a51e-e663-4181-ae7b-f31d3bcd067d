import { createElement, memo, useRef, useCallback, useState, useEffect } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import Toast from '@max/leez-toast';
import NavigationBar from '@max/leez-navigation-bar';
import { navigateBack } from '@max/meituan-uni-navigate';
import TopViewProvider from '@max/leez-top-view-provider';
import ToastManager from '@max/leez-toast-manager';
import DialogManager from '@max/leez-dialog-manager';
import LoadingManager from '@max/leez-loading-manager';

import { MRN_GET__dzim_pilot_assistant_beauty_styles, Query } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import { MRN_GET__dzim_pilot_assistant_hair_task_submit } from '@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit';
import { MRN_GET__dzim_pilot_assistant_beauty_task } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_task';
import { REQUEST_PLATFORM } from '@/utils';

import Preview from './components/Preview/index';
import StyleList, { StyleImage } from './components/StyleList/index';

// 款式生成状态枚举
export enum StyleGenerationStatus {
  NOT_GENERATED = 'not_generated',    // 未生成
  GENERATING = 'generating',          // 生成中
  GENERATED = 'generated',            // 已生成
  FAILED = 'failed'                   // 生成失败
}

// 款式状态管理接口
export interface StyleState {
  status: StyleGenerationStatus;      // 当前状态
  generatedImageUrl?: string;         // 生成的图片URL
  taskId?: number;                    // 任务ID
  error?: string;                     // 错误信息
  lastGeneratedTime?: number;         // 最后生成时间戳
}

// 轮询任务管理接口
interface PollingTask {
  taskId: number;
  styleIndex: number;
  abortController: AbortController;
}

interface DetailProps {
  type: Query['type'];
  originalUrl: string;
}

function Detail(props: DetailProps) {
  const { type = 1, originalUrl = '' } = props;

  // 基础状态
  const [styleList, setStyleList] = useState<StyleImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 款式状态管理 - 为每个款式维护独立状态
  const [styleStates, setStyleStates] = useState<Map<number, StyleState>>(new Map());

  // 轮询任务管理
  const currentPollingTask = useRef<PollingTask | null>(null);

  // 获取当前显示的图片URL
  const getCurrentImageUrl = useCallback(() => {
    if (currentIndex === 0) {
      return originalUrl; // 原图
    }

    const styleState = styleStates.get(currentIndex);
    if (styleState?.status === StyleGenerationStatus.GENERATED && styleState.generatedImageUrl) {
      return styleState.generatedImageUrl;
    }

    return originalUrl; // 默认显示原图
  }, [currentIndex, originalUrl, styleStates]);

  // 获取当前款式的状态
  const getCurrentStyleState = useCallback(() => {
    if (currentIndex === 0) {
      return { status: StyleGenerationStatus.GENERATED }; // 原图始终为已生成状态
    }

    return styleStates.get(currentIndex) || { status: StyleGenerationStatus.NOT_GENERATED };
  }, [currentIndex, styleStates]);

  // 更新款式状态
  const updateStyleState = useCallback((styleIndex: number, updates: Partial<StyleState>) => {
    setStyleStates(prev => {
      const newStates = new Map(prev);
      const currentState = newStates.get(styleIndex) || { status: StyleGenerationStatus.NOT_GENERATED };
      newStates.set(styleIndex, { ...currentState, ...updates });
      return newStates;
    });
  }, []);

  // 清理当前轮询任务
  const clearCurrentPolling = useCallback(() => {
    if (currentPollingTask.current) {
      currentPollingTask.current.abortController.abort();
      currentPollingTask.current = null;
    }
  }, []);

  // 轮询获取任务结果
  const pollTaskResult = useCallback(async (
    taskId: number,
    styleIndex: number,
    maxAttempts: number = 15
  ): Promise<string> => {
    let attempts = 0;
    const abortController = new AbortController();

    // 设置当前轮询任务
    currentPollingTask.current = { taskId, styleIndex, abortController };

    try {
      while (attempts < maxAttempts) {
        // 检查是否被取消
        if (abortController.signal.aborted) {
          throw new Error('轮询已取消');
        }

        try {
          const result = await MRN_GET__dzim_pilot_assistant_beauty_task({
            taskId,
            platform: REQUEST_PLATFORM, // 1-点评，2-美团
          });

          if (result.status === 2 && result.result) {
            // 处理成功，返回结果URL
            return result.result;
          } else if (result.status === 3) {
            // 处理失败
            throw new Error('AI生成失败');
          }

          // 状态为1（处理中），继续轮询
          attempts++;
          if (attempts < maxAttempts && !abortController.signal.aborted) {
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
          }
        } catch (error) {
          if (abortController.signal.aborted) {
            throw new Error('轮询已取消');
          }
          console.error('轮询任务结果失败:', error);
          throw error;
        }
      }

      throw new Error('AI生成超时');
    } finally {
      // 清理轮询任务引用
      if (currentPollingTask.current?.taskId === taskId) {
        currentPollingTask.current = null;
      }
    }
  }, []);

  // 生成样式图片
  const generateStyleImage = useCallback(
    async (styleIndex: number, originalImageUrl: string, styleImageUrl: string): Promise<void> => {
      try {
        // 更新状态为生成中
        updateStyleState(styleIndex, {
          status: StyleGenerationStatus.GENERATING,
          error: undefined
        });

        // 1. 提交任务
        const submitResult = await MRN_GET__dzim_pilot_assistant_hair_task_submit({
          type: String(type), // 试戴类型，1-试发型，2-试美甲，3-试染发
          platform: REQUEST_PLATFORM, // 1-点评，2-美团
          styleUrl: styleImageUrl, // 款式图
          originUrl: originalImageUrl, // 原始图
        });

        if (!submitResult.taskId) {
          throw new Error('任务提交失败');
        }

        // 更新任务ID
        updateStyleState(styleIndex, { taskId: submitResult.taskId });

        // 2. 轮询获取结果
        const generatedImageUrl = await pollTaskResult(submitResult.taskId, styleIndex);

        // 更新状态为已生成
        updateStyleState(styleIndex, {
          status: StyleGenerationStatus.GENERATED,
          generatedImageUrl,
          lastGeneratedTime: Date.now()
        });

      } catch (error) {
        console.error('发型生成失败:', error);

        // 更新状态为失败
        updateStyleState(styleIndex, {
          status: StyleGenerationStatus.FAILED,
          error: error instanceof Error ? error.message : '生成失败'
        });

        throw error;
      }
    },
    [type, pollTaskResult, updateStyleState],
  );

  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);

  const onActionPress = useCallback((index: number) => {
    console.log('onActionPress', index);
  }, []);

  // 处理款式切换
  const handleStyleSelect = useCallback(async (style: StyleImage, index: number) => {
    if (index === currentIndex) return;

    // 清理当前轮询任务
    clearCurrentPolling();

    // 切换到新的款式
    setCurrentIndex(index);

    // 如果切换到原图，直接返回
    if (index === 0) {
      return;
    }

    const currentStyleState = styleStates.get(index);

    // 根据款式状态决定处理策略
    if (!currentStyleState || currentStyleState.status === StyleGenerationStatus.NOT_GENERATED) {
      // 首次切换到该款式：触发生成
      try {
        await generateStyleImage(index, originalUrl, style.styleImageUrl || '');
        console.log(`首次生成款式: ${style.title}`, style);
      } catch (error) {
        console.error('Failed to generate style image:', error);
        Toast.open({ title: 'AI生成失败，请重试' });
      }
    } else if (currentStyleState.status === StyleGenerationStatus.GENERATED) {
      // 已生成过：直接显示，无需额外操作
      console.log(`切换到已生成款式: ${style.title}`, style);
    } else if (currentStyleState.status === StyleGenerationStatus.GENERATING) {
      // 生成中：继续轮询
      if (currentStyleState.taskId) {
        try {
          const generatedImageUrl = await pollTaskResult(currentStyleState.taskId, index);
          updateStyleState(index, {
            status: StyleGenerationStatus.GENERATED,
            generatedImageUrl,
            lastGeneratedTime: Date.now()
          });
          console.log(`继续轮询款式: ${style.title}`, style);
        } catch (error) {
          console.error('Failed to poll task result:', error);
          updateStyleState(index, {
            status: StyleGenerationStatus.FAILED,
            error: error instanceof Error ? error.message : '轮询失败'
          });
          Toast.open({ title: 'AI生成失败，请重试' });
        }
      }
    } else if (currentStyleState.status === StyleGenerationStatus.FAILED) {
      // 生成失败：重新生成
      try {
        await generateStyleImage(index, originalUrl, style.styleImageUrl || '');
        console.log(`重新生成款式: ${style.title}`, style);
      } catch (error) {
        console.error('Failed to regenerate style image:', error);
        Toast.open({ title: 'AI生成失败，请重试' });
      }
    }
  }, [currentIndex, styleStates, originalUrl, generateStyleImage, pollTaskResult, updateStyleState, clearCurrentPolling]);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      try {
        const response = await MRN_GET__dzim_pilot_assistant_beauty_styles({ type });
        if (Array.isArray(response.styleImages) && response.styleImages.length > 0) {
          setStyleList(response.styleImages);
        }
      } catch (error) {
        console.error('Failed to fetch style data:', error);
      }
    };
    initData();
  }, [type]);

  // 组件卸载时清理轮询任务
  useEffect(() => {
    return () => {
      clearCurrentPolling();
    };
  }, [clearCurrentPolling]);

  return (
    <>
      <TopViewProvider>
        <View style={styles.container}>
          <NavigationBar
            style={{ backgroundColor: '#fff' }}
            safeArea='normal'
            backIcon={{ name: 'fanhui' }}
            actionIcons={[{ name: 'fenxiang' }]}
            onBackPress={onBackPress}
            onActionPress={onActionPress}
          >
            <LText type='title3' lineClamp={1}>AI试发型</LText>
          </NavigationBar>

          {/* 预览图 */}
          <Preview
            imageUrl={getCurrentImageUrl()}
            isLoading={getCurrentStyleState().status === StyleGenerationStatus.GENERATING}
            isComplete={getCurrentStyleState().status === StyleGenerationStatus.GENERATED}
            isFailed={getCurrentStyleState().status === StyleGenerationStatus.FAILED}
            errorMessage={getCurrentStyleState().error}
            styleInfo={currentIndex > 0 ? styleList[currentIndex - 1] : undefined}
            taskId={getCurrentStyleState().taskId || 0}
          />
          {/* 款式列表 */}
          <StyleList
            list={styleList}
            selectedIndex={currentIndex}
            onStyleSelect={handleStyleSelect}
          />
        </View>
      </TopViewProvider>
      <ToastManager />
      <DialogManager />
      <LoadingManager />
    </>
  );
}

const styles = remStyleSheet({
  container: {
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    backgroundColor: '#fff',
  },
});

export default memo(Detail);
