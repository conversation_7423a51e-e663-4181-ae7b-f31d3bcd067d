import { createElement, memo, useCallback, useState, useEffect } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import NavigationBar from '@max/leez-navigation-bar';
import { navigateBack } from '@max/meituan-uni-navigate';
import TopViewProvider from '@max/leez-top-view-provider';
import ToastManager from '@max/leez-toast-manager';
import DialogManager from '@max/leez-dialog-manager';
import LoadingManager from '@max/leez-loading-manager';

import { MRN_GET__dzim_pilot_assistant_beauty_styles, Query } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';

import Preview from './components/Preview/index';
import StyleList, { StyleImage } from './components/StyleList/index';

interface DetailProps {
  type: Query['type'];
  originalUrl: string;
}

function Detail(props: DetailProps) {
  const { type = 1, originalUrl = '' } = props;

  // 基础状态
  const [styleList, setStyleList] = useState<StyleImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 获取当前选中的款式信息
  const getCurrentStyle = useCallback(() => {
    if (currentIndex === 0) {
      return null; // 原图
    }

    return styleList[currentIndex - 1] || null;
  }, [currentIndex, styleList]);

  // 判断是否是原图
  const isOriginal = currentIndex === 0;

  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);

  const onActionPress = useCallback((index: number) => {
    console.log('onActionPress', index);
  }, []);

  // 处理款式切换
  const handleStyleSelect = useCallback((style: StyleImage, index: number) => {
    if (index === currentIndex) {
      return;
    }

    // 切换到新的款式
    setCurrentIndex(index);
    console.log(`切换到款式: ${index === 0 ? '原图' : style.title}`, style);
  }, [currentIndex]);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      try {
        const response = await MRN_GET__dzim_pilot_assistant_beauty_styles({ type });
        if (Array.isArray(response.styleImages) && response.styleImages.length > 0) {
          setStyleList(response.styleImages);
        }
      } catch (error) {
        console.error('Failed to fetch style data:', error);
      }
    };
    initData();
  }, [type]);

  return (
    <>
      <TopViewProvider>
        <View style={styles.container}>
          <NavigationBar
            style={{ backgroundColor: '#fff' }}
            safeArea='normal'
            backIcon={{ name: 'fanhui' }}
            actionIcons={[{ name: 'fenxiang' }]}
            onBackPress={onBackPress}
            onActionPress={onActionPress}
          >
            <LText type='title3' lineClamp={1}>AI试发型</LText>
          </NavigationBar>

          {/* 预览图 */}
          <Preview
            originalImageUrl={originalUrl}
            currentStyle={getCurrentStyle()}
            type={type}
            isOriginal={isOriginal}
          />
          {/* 款式列表 */}
          <StyleList
            list={styleList}
            selectedIndex={currentIndex}
            onStyleSelect={handleStyleSelect}
          />
        </View>
      </TopViewProvider>
      <ToastManager />
      <DialogManager />
      <LoadingManager />
    </>
  );
}

const styles = remStyleSheet({
  container: {
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    backgroundColor: '#fff',
  },
});

export default memo(Detail);
