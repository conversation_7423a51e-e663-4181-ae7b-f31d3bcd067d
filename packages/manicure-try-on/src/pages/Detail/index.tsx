import { createElement, memo, useRef, useCallback, useState, useEffect } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import Toast from '@max/leez-toast';
import NavigationBar from '@max/leez-navigation-bar';
import { navigateBack } from '@max/meituan-uni-navigate';
import TopViewProvider from '@max/leez-top-view-provider';
import ToastManager from '@max/leez-toast-manager';
import DialogManager from '@max/leez-dialog-manager';
import LoadingManager from '@max/leez-loading-manager';

import { MRN_GET__dzim_pilot_assistant_beauty_styles, Query } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import { MRN_GET__dzim_pilot_assistant_hair_task_submit } from '@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit';
import { MRN_GET__dzim_pilot_assistant_beauty_task } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_task';
import { REQUEST_PLATFORM } from '@/utils';

import Preview from './components/Preview/index';
import StyleList, { StyleImage } from './components/StyleList/index';

interface DetailProps {
  type: Query['type'];
  originalUrl: string;
}

function Detail(props: DetailProps) {
  const { type = 1, originalUrl = '' } = props;
  const [isLoading, setIsLoading] = useState(false);
  const [styleList, setStyleList] = useState<StyleImage[]>([]);
  const [currentImageUrl, setCurrentImageUrl] = useState<string>(originalUrl);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 轮询获取任务结果
  const pollTaskResult = useCallback(async (taskId: number, maxAttempts: number = 15): Promise<string> => {
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const result = await MRN_GET__dzim_pilot_assistant_beauty_task({
          taskId,
          platform: REQUEST_PLATFORM, // 1-点评，2-美团
        });

        if (result.status === 2 && result.result) {
          // 处理成功，返回结果URL
          return result.result;
        } else if (result.status === 3) {
          // 处理失败
          throw new Error('AI生成失败');
        }

        // 状态为1（处理中），继续轮询
        attempts++;
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
        }
      } catch (error) {
        console.error('轮询任务结果失败:', error);
        throw error;
      }
    }

    throw new Error('AI生成超时');
  }, []);

  // 真实的发型生成API
  const generateStyleImage = useCallback(
    async (originalImageUrl: string, styleImageUrl: string): Promise<{ generatedImageUrl: string }> => {
      try {
        // 1. 提交任务
        const submitResult = await MRN_GET__dzim_pilot_assistant_hair_task_submit({
          type: String(type), // 试戴类型，1-试发型，2-试美甲，3-试染发
          platform: REQUEST_PLATFORM, // 1-点评，2-美团
          styleUrl: styleImageUrl, // 款式图
          originUrl: originalImageUrl, // 原始图
        });

        if (!submitResult.taskId) {
          throw new Error('任务提交失败');
        }

        // 2. 轮询获取结果
        const generatedImageUrl = await pollTaskResult(submitResult.taskId);

        return { generatedImageUrl };
      } catch (error) {
        console.error('发型生成失败:', error);
        throw error;
      }
    },
    [type, pollTaskResult],
  );

  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);

  const onActionPress = useCallback((index: number) => {
    console.log('onActionPress', index);
  }, []);

  // 处理发型切换
  const handleStyleSelect = useCallback(async (style: StyleImage, index: number) => {
    if (index === currentIndex || isLoading) return;

    setCurrentIndex(index);

    // 如果切换到第一个，则直接显示原始图片
    if (index === 0) {
      setCurrentImageUrl(originalUrl);
      return;
    } else {
      setIsLoading(true);
    }

    try {
      const result = await generateStyleImage(originalUrl, style.styleImageUrl || '');
      setCurrentImageUrl(result.generatedImageUrl);
      console.log(`切换到发型: ${style.title}`, style);
    } catch (error) {
      console.error('Failed to generate style image:', error);
      // 失败时显示错误提示
      Toast.open({ title: 'AI生成失败，请重试' });
      // 重置到原始图片
      setCurrentImageUrl(originalUrl);
      setCurrentIndex(0);
    } finally {
      setIsLoading(false);
    }
  }, [currentIndex, isLoading, originalUrl, generateStyleImage]);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      try {
        const response = await MRN_GET__dzim_pilot_assistant_beauty_styles({ type });
        if (Array.isArray(response.styleImages) && response.styleImages.length > 0) {
          setStyleList(response.styleImages);
        }
      } catch (error) {
        console.error('Failed to fetch style data:', error);
      }
    };
    initData();
  }, [type]);

  return (
    <>
      <TopViewProvider>
        <View style={styles.container}>
          <NavigationBar
            style={{ backgroundColor: '#fff' }}
            safeArea='normal'
            backIcon={{ name: 'fanhui' }}
            actionIcons={[{ name: 'fenxiang' }]}
            onBackPress={onBackPress}
            onActionPress={onActionPress}
          >
            <LText type='title3' lineClamp={1}>AI试发型</LText>
          </NavigationBar>

          {/* 预览图 */}
          <Preview
            imageUrl={currentImageUrl}
            isLoading={isLoading}
            isComplete={!isLoading}
            styleInfo={currentIndex > 0 ? styleList[currentIndex - 1] : undefined}
            taskId={0} // 暂时使用0作为占位
          />
          {/* 款式列表 */}
          <StyleList
            list={styleList}
            selectedIndex={currentIndex}
            onStyleSelect={handleStyleSelect}
          />
        </View>
      </TopViewProvider>
      <ToastManager />
      <DialogManager />
      <LoadingManager />
    </>
  );
}

const styles = remStyleSheet({
  container: {
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    backgroundColor: '#fff',
  },
});

export default memo(Detail);
