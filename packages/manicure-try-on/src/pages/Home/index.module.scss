html,
body {
  display: flex;
}

page,
html,
body {
  height: 100%;
  background-color: #f4f4f5;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 200rpx;
  background-color: red;

  // align-items: center;
  // justify-content: space-evenly;
}

.textContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 45rpx;
  font-weight: bold;
  margin: 20rpx 24rpx;
}

.info {
  font-size: 36rpx;
  margin: 8rpx 24rpx;
  color: #555;
}

.changeStyleContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
  background-color: antiquewhite;
}
.changeStyleContent {
  width: 667rpx;
  background-color: blue;
  border-radius: 24rpx;
  padding: 18rpx 24rpx;
}

.buttonContainer {
  margin-top: 36rpx;
  display: flex;
  align-items: center;
}

.photoButton {
  width: 690rpx;
  height: 96rpx;
  background: #fc89b0;
  // background: linear-gradient(98deg, #ff8fb6 0%, #ff3c87 100%);
  border-radius: 49rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
