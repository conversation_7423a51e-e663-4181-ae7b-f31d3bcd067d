import { createElement, useState, useCallback, memo, useMemo, useEffect, useRef } from '@max/max';
import View from '@hfe/max-view';
import Text from '@hfe/max-text';
import NavBar from '@/components/common/NavBar';
import StyleTab from '@/components/StyleTab';
import ChangeStyle, { ChangeStyleHandles } from '@/components/ChangeStyle';
// Leez接入： https://km.sankuai.com/collabpage/1744524702
import Button from '@hfe/max-button';
import { styleImagesMap } from './const';
import { openCamera } from '@/utils/bridge';
import { Animated } from '@max/mbc-animated';

import styles from './index.module.scss';
import { request } from '@max/meituan-uni-network';



function Home() {
  //  新增，设置主题(餐和综可省略这一步)
  const [styleImages, setStyleImages] = useState(styleImagesMap.hair);

  const changeStyleRef = useRef<ChangeStyleHandles>(null);

  const handleTabChange = useCallback((item: string) => {
    console.log('Tab changed, stopping animation.', styleImages);
    setStyleImages(styleImagesMap[item]);
    changeStyleRef.current?.stopAnimation();
  }, []);

  const scrollY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    console.log('HHHHHome');
    // 新版网络请求（版本 >= 2.0.0），MRN和WEB端默认会接入ELink, 并开启上报开关。文档：https://km.sankuai.com/collabpage/2193577916
    // 注意：web端上报，需要在build.js的define字段中定义owl: {catKey: 'xxx'}
    request({
      url: 'https://yapi.sankuai.com/mock/14789/trip/list',
      method: 'GET',
      _meituan: {
        // MRN端可以在这关闭ELink 日志上报
        // enableElinkLog: false,
        mrnChannel: 'travel',
        requestType: 'MRNUtils',
      },
      _web: {
        // Web端可以在这关闭ELink 日志上报
        // enableElinkLog: false,
      },
    })
      .then((res) => {
        console.log('请求数据成功', res);
      })
      .catch((err) => {
        console.log('请求数据失败', err);
      });
  }, []);

  const handleCamera = useCallback((sourceType: 'gallery' | 'camera') => {
    openCamera({
      count: 1,
      source: sourceType,
    }).then((res) => {
      console.log('getURl', res);
      console.log('跳转')
    });
  }, []);
  return (
    <View>
      <NavBar scrollY={scrollY} leftText="AI试款" />
      <StyleTab onTabChange={handleTabChange} />
      <View className={styles.changeStyleContainer}>
        <View className={styles.changeStyleContent}>
          <ChangeStyle images={styleImages} ref={changeStyleRef} animationDuration={2000} />
        </View>
      </View>
      <View className={styles.buttonContainer}>
        <Button
          onPress={() => {
            handleCamera('camera');
          }}
          className={styles.photoButton}
        >
          <Text>拍摄正脸照片</Text>
        </Button>
        <Button
          onPress={() => {
            handleCamera('gallery');
          }}
          className={styles.photoButton}
        >
          <Text>从相册中选取</Text>
        </Button>
      </View>
    </View>
  );
}

// 函数式组件每次调用都会刷新，使用 memo 包裹，当 props 没有变化时可以避免重复刷新
// Component 必须是 default 导出，一个文件只能存在一个 Component
export default memo(Home);
