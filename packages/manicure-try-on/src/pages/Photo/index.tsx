import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { View, StyleSheet } from '@mrn/react-native';
import NavBar from '@/components/NavBar';
import PhotoGrid from '@/components/PhotoGrid'
import TopViewProvider from '@max/leez-top-view-provider';
import PhotoTipModal from '@/components/PromptModal';
import SaveModal from '@/components/SaveModal';
import { exampleSuccessImg, exampleErrorImg } from '@/utils/util'
import ToastManager from '@max/leez-toast-manager';
import { chooseImage } from '@max/meituan-uni-image';

// sceneToken 隐私注册
// https://privacy.sankuai.com/register/list?page=current-3_pageSize-20

const PhotoPage = (props) => {

  const [photoDate, setPhotoDate] = useState<any>([])

  const [selected, setSelected] = useState<string>('-1');

  const [tipVisible, setTipVisible] = useState<boolean>(true)

  const [saveVisible, setSaveVisible] = useState<boolean>(false)

  useEffect(() => {
    // chooseImage({
    //   sceneToken: 'dd-20f752dd436d1949',
    //   sourceType:['album']
    // })
    //   .then((res) => console.log(res,'本地相册'))
    //   .catch((err) => console.log(err,'本地相册获取失败'));
  }, [])

  const toggleSelect = useCallback((item) => setSelected(item.id), [])

  const disabled = useMemo(() => !photoDate.find(item => item.id === selected), [photoDate, selected])

  const onSelectPhoto = useCallback(() => {
    if (!disabled) {
      console.log('点击了选择好了')
    }
  }, [disabled])

  return (
    <TopViewProvider>
      <View style={styles.container}>
        <NavBar onSelectPhoto={onSelectPhoto} disabled={disabled} />
        <PhotoGrid photos={photoDate} selected={selected} onToggle={toggleSelect} />
        <PhotoTipModal
          visible={tipVisible}
          topPhotos={exampleSuccessImg}
          bottomPhotos={exampleErrorImg}
        />
        <SaveModal
          visible={saveVisible}
          imageUrl={'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg'}
          onClose={() => setSaveVisible(false)}
        />
      </View>
      <ToastManager />
    </TopViewProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 18,
    paddingTop: 0,
  },
});

export default React.memo(PhotoPage);
