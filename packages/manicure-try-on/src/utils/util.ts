

export interface IExample {
  url: string
  des: string
  icon: string
}

export const exampleSuccessImg: IExample[] = [
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '单人清晰正脸',
    icon: ''
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '保持面部居中',
    icon: ''
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '光线充足',
    icon: ''
  }
]
export const exampleErrorImg: IExample[] = [
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '非正面/模糊',
    icon: ''
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '面部有遮挡',
    icon: ''
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '光线过暗',
    icon: ''
  }
]
